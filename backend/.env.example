# Environment Configuration Template
# Copy this file to .env and update the values

# Security
SECRET_KEY=your-super-secret-key-here-change-this-in-production

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:5001,http://127.0.0.1:5001

# Database Configuration
DATABASE_URL=sqlite:///database/app.db

# JWT Configuration
JWT_ACCESS_TOKEN_EXPIRES=3600  # 1 hour in seconds
JWT_REFRESH_TOKEN_EXPIRES=2592000  # 30 days in seconds

# Application Configuration
FLASK_ENV=development
DEBUG=true

# Production settings (uncomment for production)
# FLASK_ENV=production
# DEBUG=false
# SECRET_KEY=generate-a-strong-secret-key-for-production
# JWT_ACCESS_TOKEN_EXPIRES=1800  # 30 minutes for production
