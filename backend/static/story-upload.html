<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Story Upload - StoryChoice</title>
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #ec4899;
            --accent-color: #f59e0b;
            --bg-dark: #0f0f23;
            --bg-card: #1a1a2e;
            --bg-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --text-primary: #ffffff;
            --text-secondary: #a1a1aa;
            --text-muted: #71717a;
            --border-color: #27272a;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-gradient);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .upload-form {
            background: var(--bg-card);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-section h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem 1rem;
            background: var(--bg-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .pages-editor {
            background: var(--bg-dark);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .page-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .page-id {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .page-controls {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--bg-dark);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            color: var(--text-primary);
            border-color: var(--primary-color);
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .choice-item {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .choice-item input {
            flex: 1;
            margin-bottom: 0;
        }

        .choice-item select {
            flex: 1;
            margin-bottom: 0;
        }

        .choice-item button {
            flex-shrink: 0;
        }

        .add-choice-btn {
            width: 100%;
            margin-top: 0.5rem;
            background: rgba(99, 102, 241, 0.2);
            border: 1px dashed var(--primary-color);
            color: var(--primary-color);
        }

        .add-choice-btn:hover {
            background: rgba(99, 102, 241, 0.3);
        }

        .validation-results {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .validation-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .validation-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: var(--error-color);
            color: var(--error-color);
        }

        .validation-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: var(--warning-color);
            color: var(--warning-color);
        }

        .error-list {
            list-style: none;
            margin-top: 0.5rem;
        }

        .error-list li {
            margin-bottom: 0.25rem;
            padding-left: 1rem;
            position: relative;
        }

        .error-list li::before {
            content: "•";
            position: absolute;
            left: 0;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .schema-help {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .schema-help h4 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .schema-help ul {
            margin-left: 1rem;
            color: var(--text-secondary);
        }

        .schema-help li {
            margin-bottom: 0.25rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .choice-item {
                flex-direction: column;
                align-items: stretch;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 Story Upload</h1>
            <p>Erstelle und lade interaktive Geschichten mit Entscheidungsbäumen hoch</p>
        </div>

        <div class="schema-help">
            <h4>📋 Story-Schema Anforderungen:</h4>
            <ul>
                <li><strong>Entscheidungspunkte:</strong> Alle 4-5 Seiten eine Verzweigung mit Option A und B</li>
                <li><strong>Pfad-Eigenständigkeit:</strong> Jeder Entscheidungspfad hat einzigartigen Handlungsverlauf</li>
                <li><strong>Finale Vereinigung:</strong> Alle Pfade münden in finale 3er-Verzweigung</li>
                <li><strong>Textlänge:</strong> 2-4 Sätze pro Seite</li>
                <li><strong>Seiten-IDs:</strong> Eindeutige Nummerierung (1, 2A, 2B, 3A1, 3A2, etc.)</li>
            </ul>
        </div>

        <form id="storyForm" class="upload-form">
            <!-- Story Metadata -->
            <div class="form-section">
                <h3>📖 Story-Informationen</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="title">Titel *</label>
                        <input type="text" id="title" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="author">Autor</label>
                        <input type="text" id="author" name="author">
                    </div>
                </div>
                <div class="form-group">
                    <label for="description">Beschreibung *</label>
                    <textarea id="description" name="description" required placeholder="Kurze Beschreibung der Geschichte..."></textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="genre">Genre *</label>
                        <select id="genre" name="genre" required>
                            <option value="">Genre wählen...</option>
                            <option value="Romance">Romance</option>
                            <option value="Fantasy">Fantasy</option>
                            <option value="Mystery">Mystery</option>
                            <option value="Sci-Fi">Sci-Fi</option>
                            <option value="Thriller">Thriller</option>
                            <option value="Abenteuer">Abenteuer</option>
                            <option value="Drama">Drama</option>
                            <option value="Horror">Horror</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="reading_time">Lesedauer (Minuten) *</label>
                        <select id="reading_time" name="reading_time" required>
                            <option value="">Lesedauer wählen...</option>
                            <option value="15">15 Minuten</option>
                            <option value="30">30 Minuten</option>
                            <option value="45">45 Minuten</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="theme">Thema *</label>
                        <input type="text" id="theme" name="theme" required placeholder="z.B. Zeitreise, Geheimnis, Abenteuer">
                    </div>
                    <div class="form-group">
                        <label for="style">Stil *</label>
                        <input type="text" id="style" name="style" required placeholder="z.B. mysteriös, spannend, romantisch">
                    </div>
                </div>
                <div class="form-group">
                    <label for="start_page_id">Start-Seiten-ID *</label>
                    <input type="text" id="start_page_id" name="start_page_id" value="1" required>
                </div>
            </div>

            <!-- Pages Editor -->
            <div class="form-section">
                <h3>📄 Seiten-Editor</h3>
                <div class="pages-editor" id="pagesEditor">
                    <!-- Pages will be dynamically added here -->
                </div>
                <button type="button" class="btn btn-primary" onclick="addPage()">
                    ➕ Neue Seite hinzufügen
                </button>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="validateStory()">
                    ✅ Story validieren
                </button>
                <button type="button" class="btn btn-primary" onclick="saveStory()">
                    💾 Story speichern
                </button>
                <button type="button" class="btn btn-success" onclick="publishStory()">
                    🚀 Story veröffentlichen
                </button>
            </div>

            <!-- Loading Indicator -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Verarbeite Story...</p>
            </div>

            <!-- Validation Results -->
            <div id="validationResults"></div>
        </form>
    </div>

    <script>
        let storyData = {
            pages: {}
        };

        // Initialize with first page
        document.addEventListener('DOMContentLoaded', function() {
            addPage('1');
        });

        function addPage(pageId = '') {
            if (!pageId) {
                pageId = prompt('Seiten-ID eingeben (z.B. 2A, 3B1):');
                if (!pageId) return;
            }

            if (storyData.pages[pageId]) {
                alert('Seite mit dieser ID existiert bereits!');
                return;
            }

            storyData.pages[pageId] = {
                id: pageId,
                text: '',
                choices: [],
                is_ending: false,
                ending_type: ''
            };

            renderPages();
        }

        function removePage(pageId) {
            if (confirm(`Seite "${pageId}" wirklich löschen?`)) {
                delete storyData.pages[pageId];
                renderPages();
            }
        }

        function addChoice(pageId) {
            if (!storyData.pages[pageId].choices) {
                storyData.pages[pageId].choices = [];
            }
            
            storyData.pages[pageId].choices.push({
                text: '',
                next_page_id: ''
            });
            
            renderPages();
        }

        function removeChoice(pageId, choiceIndex) {
            storyData.pages[pageId].choices.splice(choiceIndex, 1);
            renderPages();
        }

        function renderPages() {
            const editor = document.getElementById('pagesEditor');
            editor.innerHTML = '';

            Object.keys(storyData.pages).sort().forEach(pageId => {
                const page = storyData.pages[pageId];
                const pageElement = createPageElement(pageId, page);
                editor.appendChild(pageElement);
            });
        }

        function createPageElement(pageId, page) {
            const div = document.createElement('div');
            div.className = 'page-item';
            
            const availablePages = Object.keys(storyData.pages).filter(id => id !== pageId);
            
            div.innerHTML = `
                <div class="page-header">
                    <div class="page-id">Seite: ${pageId}</div>
                    <div class="page-controls">
                        <button type="button" class="btn btn-danger" onclick="removePage('${pageId}')">
                            🗑️ Löschen
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Seitentext (2-4 Sätze) *</label>
                    <textarea 
                        placeholder="Beschreibe was auf dieser Seite passiert..."
                        onchange="updatePageText('${pageId}', this.value)"
                    >${page.text}</textarea>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" 
                               ${page.is_ending ? 'checked' : ''} 
                               onchange="updatePageEnding('${pageId}', this.checked)">
                        Dies ist eine Endseite
                    </label>
                </div>
                
                ${page.is_ending ? `
                    <div class="form-group">
                        <label>Ende-Typ</label>
                        <select onchange="updateEndingType('${pageId}', this.value)">
                            <option value="">Ende-Typ wählen...</option>
                            <option value="happy_triumph" ${page.ending_type === 'happy_triumph' ? 'selected' : ''}>Happy End - Triumph</option>
                            <option value="happy_compromise" ${page.ending_type === 'happy_compromise' ? 'selected' : ''}>Happy End - Kompromiss</option>
                            <option value="happy_twist" ${page.ending_type === 'happy_twist' ? 'selected' : ''}>Happy End - Twist</option>
                            <option value="tragic" ${page.ending_type === 'tragic' ? 'selected' : ''}>Tragisches Ende</option>
                            <option value="open" ${page.ending_type === 'open' ? 'selected' : ''}>Offenes Ende</option>
                            <option value="bittersweet" ${page.ending_type === 'bittersweet' ? 'selected' : ''}>Bittersweet Ende</option>
                        </select>
                    </div>
                ` : `
                    <div class="form-group">
                        <label>Entscheidungen</label>
                        <div id="choices-${pageId}">
                            ${page.choices.map((choice, index) => `
                                <div class="choice-item">
                                    <input type="text" 
                                           placeholder="Entscheidungstext..." 
                                           value="${choice.text}"
                                           onchange="updateChoiceText('${pageId}', ${index}, this.value)">
                                    <select onchange="updateChoiceTarget('${pageId}', ${index}, this.value)">
                                        <option value="">Zielseite wählen...</option>
                                        ${availablePages.map(id => `
                                            <option value="${id}" ${choice.next_page_id === id ? 'selected' : ''}>${id}</option>
                                        `).join('')}
                                    </select>
                                    <button type="button" class="btn btn-danger" onclick="removeChoice('${pageId}', ${index})">
                                        ❌
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                        <button type="button" class="btn add-choice-btn" onclick="addChoice('${pageId}')">
                            ➕ Entscheidung hinzufügen
                        </button>
                    </div>
                `}
            `;
            
            return div;
        }

        function updatePageText(pageId, text) {
            storyData.pages[pageId].text = text;
        }

        function updatePageEnding(pageId, isEnding) {
            storyData.pages[pageId].is_ending = isEnding;
            if (isEnding) {
                storyData.pages[pageId].choices = [];
            }
            renderPages();
        }

        function updateEndingType(pageId, endingType) {
            storyData.pages[pageId].ending_type = endingType;
        }

        function updateChoiceText(pageId, choiceIndex, text) {
            storyData.pages[pageId].choices[choiceIndex].text = text;
        }

        function updateChoiceTarget(pageId, choiceIndex, targetId) {
            storyData.pages[pageId].choices[choiceIndex].next_page_id = targetId;
        }

        function collectFormData() {
            const formData = {
                title: document.getElementById('title').value,
                description: document.getElementById('description').value,
                genre: document.getElementById('genre').value,
                reading_time: parseInt(document.getElementById('reading_time').value),
                theme: document.getElementById('theme').value,
                style: document.getElementById('style').value,
                start_page_id: document.getElementById('start_page_id').value,
                author: document.getElementById('author').value,
                pages: storyData.pages
            };

            return formData;
        }

        async function validateStory() {
            const formData = collectFormData();
            
            // Basic client-side validation
            const errors = [];
            
            if (!formData.title) errors.push('Titel ist erforderlich');
            if (!formData.description) errors.push('Beschreibung ist erforderlich');
            if (!formData.genre) errors.push('Genre ist erforderlich');
            if (!formData.reading_time) errors.push('Lesedauer ist erforderlich');
            if (!formData.theme) errors.push('Thema ist erforderlich');
            if (!formData.style) errors.push('Stil ist erforderlich');
            if (!formData.start_page_id) errors.push('Start-Seiten-ID ist erforderlich');
            
            if (Object.keys(formData.pages).length === 0) {
                errors.push('Mindestens eine Seite ist erforderlich');
            }

            // Check if start page exists
            if (formData.start_page_id && !formData.pages[formData.start_page_id]) {
                errors.push(`Start-Seite "${formData.start_page_id}" existiert nicht`);
            }

            // Validate pages
            Object.entries(formData.pages).forEach(([pageId, page]) => {
                if (!page.text) {
                    errors.push(`Seite "${pageId}": Text ist erforderlich`);
                }
                
                const sentences = page.text.split('.').filter(s => s.trim()).length;
                if (sentences < 2 || sentences > 4) {
                    errors.push(`Seite "${pageId}": Text sollte 2-4 Sätze haben (gefunden: ${sentences})`);
                }

                if (!page.is_ending && (!page.choices || page.choices.length === 0)) {
                    errors.push(`Seite "${pageId}": Nicht-End-Seiten brauchen Entscheidungen`);
                }

                if (page.choices) {
                    page.choices.forEach((choice, index) => {
                        if (!choice.text) {
                            errors.push(`Seite "${pageId}", Entscheidung ${index + 1}: Text ist erforderlich`);
                        }
                        if (!choice.next_page_id) {
                            errors.push(`Seite "${pageId}", Entscheidung ${index + 1}: Zielseite ist erforderlich`);
                        } else if (!formData.pages[choice.next_page_id]) {
                            errors.push(`Seite "${pageId}", Entscheidung ${index + 1}: Zielseite "${choice.next_page_id}" existiert nicht`);
                        }
                    });
                }

                if (page.is_ending && !page.ending_type) {
                    errors.push(`Seite "${pageId}": End-Seiten brauchen einen Ende-Typ`);
                }
            });

            displayValidationResults(errors);
            return errors.length === 0;
        }

        async function saveStory() {
            if (!(await validateStory())) {
                alert('Bitte behebe erst die Validierungsfehler!');
                return;
            }

            const formData = collectFormData();
            showLoading(true);

            try {
                const response = await fetch('/api/stories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    alert('Story erfolgreich gespeichert!');
                    displayValidationResults([], 'Story wurde erfolgreich gespeichert und validiert.');
                } else {
                    alert('Fehler beim Speichern: ' + result.error);
                    if (result.validation_errors) {
                        displayValidationResults(result.validation_errors);
                    }
                }
            } catch (error) {
                alert('Netzwerkfehler: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        async function publishStory() {
            if (!(await validateStory())) {
                alert('Bitte behebe erst die Validierungsfehler!');
                return;
            }

            const formData = collectFormData();
            formData.is_published = true;
            showLoading(true);

            try {
                const response = await fetch('/api/stories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    alert('Story erfolgreich veröffentlicht!');
                    displayValidationResults([], 'Story wurde erfolgreich veröffentlicht und ist jetzt öffentlich verfügbar.');
                } else {
                    alert('Fehler beim Veröffentlichen: ' + result.error);
                    if (result.validation_errors) {
                        displayValidationResults(result.validation_errors);
                    }
                }
            } catch (error) {
                alert('Netzwerkfehler: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        function displayValidationResults(errors, successMessage = '') {
            const resultsDiv = document.getElementById('validationResults');
            
            if (errors.length === 0 && successMessage) {
                resultsDiv.innerHTML = `
                    <div class="validation-success">
                        <h4>✅ Validierung erfolgreich</h4>
                        <p>${successMessage}</p>
                    </div>
                `;
            } else if (errors.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="validation-success">
                        <h4>✅ Story ist valide</h4>
                        <p>Alle Validierungsprüfungen bestanden. Die Story kann gespeichert oder veröffentlicht werden.</p>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="validation-error">
                        <h4>❌ Validierungsfehler gefunden</h4>
                        <ul class="error-list">
                            ${errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            if (show) {
                loading.classList.add('active');
            } else {
                loading.classList.remove('active');
            }
        }
    </script>
</body>
</html>

