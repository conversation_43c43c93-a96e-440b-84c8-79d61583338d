<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StoryChoice - Anmeldung</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#6366f1">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .auth-header h1 {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #6366f1, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .auth-tabs {
            display: flex;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 4px;
        }

        .auth-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .auth-tab.active {
            background: linear-gradient(135deg, #6366f1, #ec4899);
            color: white;
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #e2e8f0;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .auth-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #6366f1, #ec4899);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .auth-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
        }

        .auth-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #6366f1;
            text-decoration: none;
            font-weight: 500;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #6ee7b7;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #6366f1;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
            .auth-container {
                margin: 20px;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1>StoryChoice</h1>
            <p>Willkommen zurück!</p>
        </div>

        <div class="auth-tabs">
            <div class="auth-tab active" onclick="switchTab('login')">Anmelden</div>
            <div class="auth-tab" onclick="switchTab('register')">Registrieren</div>
        </div>

        <div id="error-message" class="error-message" style="display: none;"></div>
        <div id="success-message" class="success-message" style="display: none;"></div>

        <!-- Login Form -->
        <form id="login-form" class="auth-form active">
            <div class="form-group">
                <label for="login-username">Benutzername oder E-Mail</label>
                <input type="text" id="login-username" name="username" placeholder="Dein Benutzername oder E-Mail" required>
            </div>
            <div class="form-group">
                <label for="login-password">Passwort</label>
                <input type="password" id="login-password" name="password" placeholder="Dein Passwort" required>
            </div>
            <button type="submit" class="auth-button">Anmelden</button>
        </form>

        <!-- Register Form -->
        <form id="register-form" class="auth-form">
            <div class="form-group">
                <label for="register-username">Benutzername</label>
                <input type="text" id="register-username" name="username" placeholder="Wähle einen Benutzernamen" required>
            </div>
            <div class="form-group">
                <label for="register-email">E-Mail</label>
                <input type="email" id="register-email" name="email" placeholder="Deine E-Mail-Adresse" required>
            </div>
            <div class="form-group">
                <label for="register-display-name">Anzeigename (optional)</label>
                <input type="text" id="register-display-name" name="display_name" placeholder="Wie sollen andere dich sehen?">
            </div>
            <div class="form-group">
                <label for="register-role">Rolle</label>
                <select id="register-role" name="role" required>
                    <option value="reader">Leser - Geschichten lesen</option>
                    <option value="author">Autor - Geschichten erstellen</option>
                </select>
            </div>
            <div class="form-group">
                <label for="register-password">Passwort</label>
                <input type="password" id="register-password" name="password" placeholder="Mindestens 8 Zeichen" required>
            </div>
            <div class="form-group">
                <label for="register-confirm-password">Passwort bestätigen</label>
                <input type="password" id="register-confirm-password" name="confirm_password" placeholder="Passwort wiederholen" required>
            </div>
            <button type="submit" class="auth-button">Registrieren</button>
        </form>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Wird verarbeitet...</p>
        </div>

        <div class="back-link">
            <a href="/">← Zurück zur Startseite</a>
        </div>
    </div>

    <script>
        // Authentication state management
        let currentTab = 'login';

        function switchTab(tab) {
            currentTab = tab;
            
            // Update tab buttons
            document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
            document.querySelector(`.auth-tab:nth-child(${tab === 'login' ? '1' : '2'})`).classList.add('active');
            
            // Update forms
            document.querySelectorAll('.auth-form').forEach(f => f.classList.remove('active'));
            document.getElementById(`${tab}-form`).classList.add('active');
            
            // Clear messages
            hideMessages();
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('success-message').style.display = 'none';
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('error-message').style.display = 'none';
        }

        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const buttons = document.querySelectorAll('.auth-button');
            
            if (show) {
                loading.classList.add('active');
                buttons.forEach(btn => btn.disabled = true);
            } else {
                loading.classList.remove('active');
                buttons.forEach(btn => btn.disabled = false);
            }
        }

        // Login form handler
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                username: formData.get('username'),
                password: formData.get('password')
            };

            try {
                showLoading(true);
                hideMessages();

                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    // Store tokens
                    localStorage.setItem('access_token', result.tokens.access_token);
                    localStorage.setItem('refresh_token', result.tokens.refresh_token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    
                    showSuccess('Anmeldung erfolgreich! Weiterleitung...');
                    
                    // Redirect after short delay
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                } else {
                    showError(result.error || 'Anmeldung fehlgeschlagen');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Verbindungsfehler. Bitte versuche es erneut.');
            } finally {
                showLoading(false);
            }
        });

        // Register form handler
        document.getElementById('register-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const password = formData.get('password');
            const confirmPassword = formData.get('confirm_password');
            
            // Validate password confirmation
            if (password !== confirmPassword) {
                showError('Passwörter stimmen nicht überein');
                return;
            }

            const data = {
                username: formData.get('username'),
                email: formData.get('email'),
                display_name: formData.get('display_name'),
                role: formData.get('role'),
                password: password
            };

            try {
                showLoading(true);
                hideMessages();

                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    // Store tokens
                    localStorage.setItem('access_token', result.tokens.access_token);
                    localStorage.setItem('refresh_token', result.tokens.refresh_token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    
                    showSuccess('Registrierung erfolgreich! Weiterleitung...');
                    
                    // Redirect after short delay
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                } else {
                    if (result.validation_errors) {
                        showError(result.validation_errors.join(', '));
                    } else {
                        showError(result.error || 'Registrierung fehlgeschlagen');
                    }
                }
            } catch (error) {
                console.error('Registration error:', error);
                showError('Verbindungsfehler. Bitte versuche es erneut.');
            } finally {
                showLoading(false);
            }
        });

        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('Service Worker registered successfully:', registration.scope);
                } catch (error) {
                    console.error('Service Worker registration failed:', error);
                }
            });
        }

        // Check if user is already logged in
        window.addEventListener('load', () => {
            const token = localStorage.getItem('access_token');
            if (token) {
                // User is already logged in, redirect to main page
                window.location.href = '/';
            }
        });
    </script>
</body>
</html>
