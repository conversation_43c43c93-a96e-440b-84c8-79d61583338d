<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StoryChoice - Interaktive Geschichten</title>
    <meta name="description" content="Tauche ein in interaktive Geschichten, wo jede Entscheidung zählt. Erlebe personalisierte Abenteuer auf deinem Smartphone.">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#1a1a2e">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #ec4899;
            --accent-color: #f59e0b;
            --bg-dark: #0f0f23;
            --bg-card: #1a1a2e;
            --bg-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --text-primary: #ffffff;
            --text-secondary: #a1a1aa;
            --text-muted: #71717a;
            --border-color: #27272a;
            --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            --shadow-xl: 0 35px 60px -12px rgba(0, 0, 0, 0.6);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-gradient);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .cta-button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            padding-top: 100px;
        }

        .hero-text h1 {
            font-family: 'Playfair Display', serif;
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-text p {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.7;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            padding: 1rem 2rem;
            border: 2px solid var(--border-color);
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .phone-mockup {
            width: 300px;
            height: 600px;
            background: linear-gradient(135deg, var(--bg-card), #2a2a4e);
            border-radius: 30px;
            padding: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: var(--bg-dark);
            border-radius: 20px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .story-preview {
            background: var(--bg-card);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid var(--border-color);
        }

        .story-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .story-text {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .choice-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .choice-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .choice-btn:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        /* Features Section */
        .features {
            padding: 100px 0;
            background: rgba(26, 26, 46, 0.3);
        }

        .section-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title h2 {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .section-title p {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: var(--bg-card);
            padding: 2rem;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Stories Section */
        .stories {
            padding: 100px 0;
        }

        .stories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .story-card {
            background: var(--bg-card);
            border-radius: 20px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
        }

        .story-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .story-image {
            height: 200px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
        }

        .story-content {
            padding: 1.5rem;
        }

        .story-genre {
            color: var(--primary-color);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .story-card h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .story-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .story-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
        }

        /* Download Section */
        .download {
            padding: 100px 0;
            background: rgba(26, 26, 46, 0.3);
            text-align: center;
        }

        .download-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .download h2 {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .download p {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
        }

        .download-options {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            margin-bottom: 3rem;
        }

        .download-btn {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 1.5rem 2rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
            min-width: 200px;
        }

        .download-btn:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .download-icon {
            font-size: 2rem;
        }

        .download-text {
            text-align: left;
        }

        .download-text .small {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .download-text .large {
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* Footer */
        footer {
            background: var(--bg-dark);
            padding: 50px 0 30px;
            border-top: 1px solid var(--border-color);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-section ul li a:hover {
            color: var(--primary-color);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            color: var(--text-muted);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .hero-buttons {
                justify-content: center;
            }

            .phone-mockup {
                width: 250px;
                height: 500px;
            }

            .features-grid,
            .stories-grid {
                grid-template-columns: 1fr;
            }

            .download-options {
                flex-direction: column;
                align-items: center;
            }

            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .animate-fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* Scroll animations */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }

        .scroll-animate.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <div class="logo">StoryChoice</div>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#stories">Geschichten</a></li>
                <li><a href="#download">Download</a></li>
                <li><a href="#community">Community</a></li>
            </ul>
            <a href="#download" class="cta-button">App installieren</a>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text animate-fade-in">
                    <h1>Deine Geschichte.<br>Deine Entscheidungen.<br>Dein Abenteuer.</h1>
                    <p>Tauche ein in interaktive Geschichten, wo jede Wahl zählt. Erlebe personalisierte Abenteuer, die sich an deine Entscheidungen anpassen und dich in den Bann ziehen.</p>
                    <div class="hero-buttons">
                        <a href="#download" class="btn-primary">
                            📱 Jetzt kostenlos starten
                        </a>
                        <a href="#stories" class="btn-secondary">Geschichten entdecken</a>
                    </div>
                </div>
                <div class="hero-visual animate-fade-in">
                    <div class="phone-mockup animate-pulse">
                        <div class="phone-screen">
                            <div class="story-preview">
                                <div class="story-title">Das Geheimnis der Zeitreise</div>
                                <div class="story-text">Du stehst vor einer mysteriösen Tür. Ein seltsames Leuchten dringt durch die Ritzen. Was machst du?</div>
                                <div class="choice-buttons">
                                    <button class="choice-btn">🚪 Die Tür öffnen</button>
                                    <button class="choice-btn">🔍 Erst genauer untersuchen</button>
                                    <button class="choice-btn">🏃 Weglaufen</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-title scroll-animate">
                <h2>Warum StoryChoice?</h2>
                <p>Entdecke die Features, die deine Lese-Erfahrung revolutionieren</p>
            </div>
            <div class="features-grid">
                <div class="feature-card scroll-animate">
                    <div class="feature-icon">📱</div>
                    <h3>Offline verfügbar</h3>
                    <p>Lade deine Lieblingsgeschichten herunter und lese sie überall - auch ohne Internetverbindung. Perfekt für unterwegs.</p>
                </div>
                <div class="feature-card scroll-animate">
                    <div class="feature-icon">🎭</div>
                    <h3>Personalisierte Geschichten</h3>
                    <p>Jede Entscheidung formt deine einzigartige Geschichte. Erlebe verschiedene Enden und Wendungen basierend auf deinen Wahlen.</p>
                </div>
                <div class="feature-card scroll-animate">
                    <div class="feature-icon">🌟</div>
                    <h3>Vielfältige Genres</h3>
                    <p>Von Romance über Fantasy bis hin zu Thriller - entdecke Geschichten in jedem Genre, das dein Herz begehrt.</p>
                </div>
                <div class="feature-card scroll-animate">
                    <div class="feature-icon">👥</div>
                    <h3>Community Features</h3>
                    <p>Teile deine Erfahrungen, diskutiere mit anderen Lesern und entdecke neue Geschichten durch Empfehlungen.</p>
                </div>
                <div class="feature-card scroll-animate">
                    <div class="feature-icon">💎</div>
                    <h3>Premium Inhalte</h3>
                    <p>Schalte exklusive Geschichten und besondere Entscheidungsoptionen frei für noch intensivere Erlebnisse.</p>
                </div>
                <div class="feature-card scroll-animate">
                    <div class="feature-icon">🔄</div>
                    <h3>Geräte-Synchronisation</h3>
                    <p>Starte auf dem Smartphone und setze auf dem Tablet fort. Dein Fortschritt wird automatisch synchronisiert.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stories Section -->
    <section id="stories" class="stories">
        <div class="container">
            <div class="section-title scroll-animate">
                <h2>Beliebte Geschichten</h2>
                <p>Entdecke die meist gelesenen interaktiven Abenteuer</p>
            </div>
            <div class="stories-grid">
                <div class="story-card scroll-animate">
                    <div class="story-image">💕</div>
                    <div class="story-content">
                        <div class="story-genre">Romance</div>
                        <h3>Liebe in Paris</h3>
                        <p class="story-description">Eine zufällige Begegnung in einem Pariser Café verändert alles. Wirst du dein Herz öffnen oder auf Nummer sicher gehen?</p>
                        <div class="story-stats">
                            <div class="rating">⭐ 4.8 (2.1k Bewertungen)</div>
                            <div>12 Kapitel</div>
                        </div>
                    </div>
                </div>
                <div class="story-card scroll-animate">
                    <div class="story-image">🗡️</div>
                    <div class="story-content">
                        <div class="story-genre">Fantasy</div>
                        <h3>Die Drachenkönige</h3>
                        <p class="story-description">Als letzter Drachenreiter musst du das Königreich vor der Dunkelheit retten. Jede Entscheidung kann über Leben und Tod entscheiden.</p>
                        <div class="story-stats">
                            <div class="rating">⭐ 4.9 (3.5k Bewertungen)</div>
                            <div>18 Kapitel</div>
                        </div>
                    </div>
                </div>
                <div class="story-card scroll-animate">
                    <div class="story-image">🔍</div>
                    <div class="story-content">
                        <div class="story-genre">Mystery</div>
                        <h3>Das verschwundene Erbe</h3>
                        <p class="story-description">Ein mysteriöser Brief führt dich zu einem alten Herrenhaus. Kannst du das Rätsel lösen, bevor es zu spät ist?</p>
                        <div class="story-stats">
                            <div class="rating">⭐ 4.7 (1.8k Bewertungen)</div>
                            <div>15 Kapitel</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="download">
        <div class="container">
            <div class="download-content scroll-animate">
                <h2>Starte dein Abenteuer</h2>
                <p>Installiere StoryChoice als Progressive Web App und genieße eine native App-Erfahrung direkt in deinem Browser.</p>
                
                <div class="download-options">
                    <a href="#" class="download-btn" onclick="installPWA()">
                        <div class="download-icon">📱</div>
                        <div class="download-text">
                            <div class="small">Installieren als</div>
                            <div class="large">Progressive Web App</div>
                        </div>
                    </a>
                    <a href="#" class="download-btn">
                        <div class="download-icon">🌐</div>
                        <div class="download-text">
                            <div class="small">Direkt im</div>
                            <div class="large">Browser starten</div>
                        </div>
                    </a>
                </div>

                <div style="background: var(--bg-card); padding: 2rem; border-radius: 15px; border: 1px solid var(--border-color); max-width: 400px; margin: 0 auto;">
                    <h3 style="margin-bottom: 1rem;">QR-Code scannen</h3>
                    <div style="width: 150px; height: 150px; background: white; margin: 0 auto; border-radius: 10px; display: flex; align-items: center; justify-content: center; font-size: 3rem;">📱</div>
                    <p style="margin-top: 1rem; color: var(--text-secondary); font-size: 0.9rem;">Scanne mit deinem Smartphone für die mobile Installation</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>StoryChoice</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 1rem;">Die Zukunft des interaktiven Storytellings. Erlebe Geschichten, die sich an deine Entscheidungen anpassen.</p>
                </div>
                <div class="footer-section">
                    <h3>Geschichten</h3>
                    <ul>
                        <li><a href="#">Romance</a></li>
                        <li><a href="#">Fantasy</a></li>
                        <li><a href="#">Mystery</a></li>
                        <li><a href="#">Sci-Fi</a></li>
                        <li><a href="#">Thriller</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Community</h3>
                    <ul>
                        <li><a href="#">Creator werden</a></li>
                        <li><a href="#">Discord</a></li>
                        <li><a href="#">Reddit</a></li>
                        <li><a href="#">Blog</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Hilfe-Center</a></li>
                        <li><a href="#">Kontakt</a></li>
                        <li><a href="#">Datenschutz</a></li>
                        <li><a href="#">AGB</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 StoryChoice. Alle Rechte vorbehalten.</p>
            </div>
        </div>
    </footer>

    <script>
        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-animate').forEach(el => {
            observer.observe(el);
        });

        // Interactive choice buttons
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.background = 'linear-gradient(135deg, var(--secondary-color), var(--accent-color))';
                this.textContent = '✓ ' + this.textContent;
                
                // Disable other buttons
                document.querySelectorAll('.choice-btn').forEach(otherBtn => {
                    if (otherBtn !== this) {
                        otherBtn.style.opacity = '0.5';
                        otherBtn.style.pointerEvents = 'none';
                    }
                });

                // Reset after 2 seconds
                setTimeout(() => {
                    document.querySelectorAll('.choice-btn').forEach(resetBtn => {
                        resetBtn.style.background = 'linear-gradient(135deg, var(--primary-color), var(--primary-dark))';
                        resetBtn.style.opacity = '1';
                        resetBtn.style.pointerEvents = 'auto';
                        resetBtn.textContent = resetBtn.textContent.replace('✓ ', '');
                    });
                }, 2000);
            });
        });

        // PWA Installation
        let deferredPrompt;

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
        });

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('PWA installation accepted');
                    }
                    deferredPrompt = null;
                });
            } else {
                alert('Um StoryChoice zu installieren, nutze das "Zur Startseite hinzufügen" Feature deines Browsers.');
            }
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header background on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(15, 15, 35, 0.98)';
            } else {
                header.style.background = 'rgba(15, 15, 35, 0.95)';
            }
        });
    </script>
</body>
</html>

