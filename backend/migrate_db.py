#!/usr/bin/env python3
"""
Database migration script to add authentication tables
"""
import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from main import app, db
from user import User, UserSession, UserRole
from story import Story

def migrate_database():
    """Migrate the database to add authentication tables"""
    with app.app_context():
        print("Starting database migration...")
        
        # Create all tables
        db.create_all()
        print("✅ Database tables created/updated")
        
        # Check if we need to create a default admin user
        admin_user = User.query.filter_by(role=UserRole.ADMIN).first()
        if not admin_user:
            print("Creating default admin user...")
            
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password='admin123',  # Change this in production!
                role=UserRole.ADMIN,
                display_name='Administrator'
            )
            admin_user.is_verified = True
            
            db.session.add(admin_user)
            db.session.commit()
            
            print("✅ Default admin user created:")
            print("   Username: admin")
            print("   Email: <EMAIL>")
            print("   Password: admin123")
            print("   ⚠️  CHANGE THE PASSWORD IN PRODUCTION!")
        
        # Create a default author user for testing
        author_user = User.query.filter_by(username='author').first()
        if not author_user:
            print("Creating default author user...")
            
            author_user = User(
                username='author',
                email='<EMAIL>',
                password='author123',
                role=UserRole.AUTHOR,
                display_name='Story Author'
            )
            author_user.is_verified = True
            
            db.session.add(author_user)
            db.session.commit()
            
            print("✅ Default author user created:")
            print("   Username: author")
            print("   Email: <EMAIL>")
            print("   Password: author123")
        
        # Create a default reader user for testing
        reader_user = User.query.filter_by(username='reader').first()
        if not reader_user:
            print("Creating default reader user...")
            
            reader_user = User(
                username='reader',
                email='<EMAIL>',
                password='reader123',
                role=UserRole.READER,
                display_name='Story Reader'
            )
            reader_user.is_verified = True
            
            db.session.add(reader_user)
            db.session.commit()
            
            print("✅ Default reader user created:")
            print("   Username: reader")
            print("   Email: <EMAIL>")
            print("   Password: reader123")
        
        # Migrate existing stories to have author relationships
        print("Migrating existing stories...")
        stories_without_author_id = Story.query.filter_by(author_id=None).all()
        
        if stories_without_author_id:
            # Assign existing stories to the admin user
            for story in stories_without_author_id:
                story.author_id = admin_user.id
                if not story.author:
                    story.author = admin_user.display_name
            
            db.session.commit()
            print(f"✅ Migrated {len(stories_without_author_id)} existing stories to admin user")
        
        # Clean up expired sessions
        from user import UserSession
        expired_count = UserSession.cleanup_expired()
        if expired_count > 0:
            print(f"✅ Cleaned up {expired_count} expired sessions")
        
        print("\n🎉 Database migration completed successfully!")
        print("\nDefault users created for testing:")
        print("- Admin: admin / admin123")
        print("- Author: author / author123") 
        print("- Reader: reader / reader123")
        print("\n⚠️  Remember to change default passwords in production!")
        
        return True

def rollback_migration():
    """Rollback migration (for development only)"""
    with app.app_context():
        print("⚠️  Rolling back migration...")
        
        # Drop user-related tables
        UserSession.__table__.drop(db.engine, checkfirst=True)
        User.__table__.drop(db.engine, checkfirst=True)
        
        # Reset author_id in stories table
        try:
            db.engine.execute("UPDATE stories SET author_id = NULL")
            print("✅ Reset author_id in stories")
        except:
            print("ℹ️  Could not reset author_id (table might not exist)")
        
        print("✅ Migration rolled back")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Database migration for authentication')
    parser.add_argument('--rollback', action='store_true', help='Rollback the migration')
    
    args = parser.parse_args()
    
    if args.rollback:
        if input("Are you sure you want to rollback? This will delete all user data! (yes/no): ") == "yes":
            rollback_migration()
        else:
            print("Rollback cancelled")
    else:
        migrate_database()
