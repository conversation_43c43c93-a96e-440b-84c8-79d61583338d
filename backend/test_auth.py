#!/usr/bin/env python3
"""
Authentication system testing script
"""
import requests
import json
import sys
import time

BASE_URL = "http://localhost:5001"

class AuthTester:
    def __init__(self):
        self.access_token = None
        self.refresh_token = None
        self.user_data = None

    def test_user_registration(self):
        """Test user registration"""
        print("Testing user registration...")
        
        test_users = [
            {
                "username": "testauthor",
                "email": "<EMAIL>",
                "password": "testpass123",
                "role": "author",
                "display_name": "Test Author"
            },
            {
                "username": "testreader",
                "email": "<EMAIL>", 
                "password": "testpass123",
                "role": "reader",
                "display_name": "Test Reader"
            }
        ]
        
        for user_data in test_users:
            try:
                response = requests.post(
                    f"{BASE_URL}/api/auth/register",
                    json=user_data,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"Registration for {user_data['username']}: {response.status_code}")
                result = response.json()
                
                if result.get('success'):
                    print(f"✅ Successfully registered {user_data['username']}")
                    if user_data['role'] == 'author':
                        # Store tokens for further testing
                        self.access_token = result['tokens']['access_token']
                        self.refresh_token = result['tokens']['refresh_token']
                        self.user_data = result['user']
                else:
                    print(f"❌ Registration failed: {result.get('error')}")
                    if 'validation_errors' in result:
                        for error in result['validation_errors']:
                            print(f"   - {error}")
                            
            except Exception as e:
                print(f"❌ Registration error for {user_data['username']}: {e}")
        
        return self.access_token is not None

    def test_user_login(self):
        """Test user login"""
        print("\nTesting user login...")
        
        login_data = {
            "username": "testauthor",
            "password": "testpass123"
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"Login status: {response.status_code}")
            result = response.json()
            
            if result.get('success'):
                print("✅ Login successful")
                self.access_token = result['tokens']['access_token']
                self.refresh_token = result['tokens']['refresh_token']
                self.user_data = result['user']
                print(f"   User: {self.user_data['username']} ({self.user_data['role']})")
                return True
            else:
                print(f"❌ Login failed: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False

    def test_protected_endpoints(self):
        """Test protected endpoints"""
        print("\nTesting protected endpoints...")
        
        if not self.access_token:
            print("❌ No access token available")
            return False
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        # Test /api/auth/me
        try:
            response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
            print(f"GET /api/auth/me: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ User info retrieved: {result['user']['username']}")
            else:
                print(f"❌ Failed to get user info: {response.json()}")
                
        except Exception as e:
            print(f"❌ Error getting user info: {e}")
        
        # Test story creation with authentication
        test_story = {
            "title": "Authenticated Test Story",
            "description": "A test story created by authenticated user",
            "genre": "Mystery",
            "reading_time": 5,
            "theme": "testing, authentication",
            "style": "simple, direct",
            "start_page_id": "1",
            "pages": {
                "1": {
                    "id": "1",
                    "text": "This is an authenticated test story. What do you do?",
                    "choices": [
                        {"text": "Continue", "next_page_id": "2"}
                    ]
                },
                "2": {
                    "id": "2",
                    "text": "You continued successfully!",
                    "is_ending": True,
                    "ending_type": "happy_triumph"
                }
            }
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/stories",
                json=test_story,
                headers=headers
            )
            
            print(f"POST /api/stories (authenticated): {response.status_code}")
            result = response.json()
            
            if result.get('success'):
                print(f"✅ Story created successfully: {result['story_id']}")
                return result['story_id']
            else:
                print(f"❌ Story creation failed: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ Error creating story: {e}")
        
        return None

    def test_role_based_access(self):
        """Test role-based access control"""
        print("\nTesting role-based access control...")
        
        # Test with reader account
        reader_login = {
            "username": "testreader",
            "password": "testpass123"
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/auth/login",
                json=reader_login,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                reader_token = response.json()['tokens']['access_token']
                
                # Try to create story as reader (should fail)
                test_story = {
                    "title": "Reader Test Story",
                    "description": "This should fail",
                    "genre": "Mystery",
                    "reading_time": 5,
                    "theme": "testing",
                    "style": "simple",
                    "start_page_id": "1",
                    "pages": {
                        "1": {
                            "id": "1",
                            "text": "Test.",
                            "is_ending": True,
                            "ending_type": "tragic"
                        }
                    }
                }
                
                response = requests.post(
                    f"{BASE_URL}/api/stories",
                    json=test_story,
                    headers={
                        "Authorization": f"Bearer {reader_token}",
                        "Content-Type": "application/json"
                    }
                )
                
                print(f"Reader story creation attempt: {response.status_code}")
                
                if response.status_code == 403:
                    print("✅ Role-based access control working - reader cannot create stories")
                else:
                    print("❌ Role-based access control failed - reader should not be able to create stories")
                    
        except Exception as e:
            print(f"❌ Error testing role-based access: {e}")

    def test_logout(self):
        """Test user logout"""
        print("\nTesting logout...")
        
        if not self.access_token:
            print("❌ No access token available")
            return False
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/auth/logout",
                headers={"Authorization": f"Bearer {self.access_token}"}
            )
            
            print(f"Logout status: {response.status_code}")
            result = response.json()
            
            if result.get('success'):
                print("✅ Logout successful")
                self.access_token = None
                self.refresh_token = None
                self.user_data = None
                return True
            else:
                print(f"❌ Logout failed: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"❌ Logout error: {e}")
            return False

def main():
    print("Authentication System Testing")
    print("=" * 50)
    
    tester = AuthTester()
    
    # Run tests
    tests = [
        ("User Registration", tester.test_user_registration),
        ("User Login", tester.test_user_login),
        ("Protected Endpoints", tester.test_protected_endpoints),
        ("Role-based Access", tester.test_role_based_access),
        ("User Logout", tester.test_logout)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASSED" if results[i] else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for r in results if r)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All authentication tests passed!")
        sys.exit(0)
    else:
        print("⚠️  Some authentication tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
