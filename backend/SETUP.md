# StoryChoice PWA - Setup Guide

## Phase 1 Implementation Complete ✅

This guide covers the setup and testing of the Phase 1 critical fixes that have been implemented.

## What's Been Implemented

### ✅ 1. API Endpoints
- **GET /api/stories** - Retrieve all published stories with filtering
- **GET /api/stories/<id>** - Get specific story by ID
- **POST /api/stories** - Create new story with validation
- **POST /api/stories/<id>/validate** - Validate story structure
- **POST /api/stories/<id>/publish** - Publish a story
- **GET /api/genres** - Get available genres

### ✅ 2. Security Improvements
- Environment variable support for configuration
- Input validation and sanitization
- Improved CORS security
- Debug mode configuration
- Fixed sentence validation bug

### ✅ 3. PWA Service Worker
- Offline functionality with caching strategies
- Network-first for API requests
- Cache-first for static files
- Enhanced PWA manifest with required fields

### ✅ 4. Error Handling
- Comprehensive error handlers for all HTTP status codes
- Database rollback on errors
- Frontend error notifications
- Improved validation with detailed error messages

## Setup Instructions

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy the environment template:
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
SECRET_KEY=your-super-secret-key-here
ALLOWED_ORIGINS=http://localhost:5001,http://127.0.0.1:5001
DEBUG=true
```

### 3. Initialize Database

```bash
cd backend
python populate_db.py
```

### 4. Start the Application

```bash
cd backend/src
python main.py
```

The application will be available at: http://localhost:5001

## Testing the Implementation

### 1. Test API Endpoints

Run the API test script:
```bash
cd backend
python test_api.py
```

### 2. Manual Testing

1. **Main Page**: Visit http://localhost:5001
2. **Story Upload**: Visit http://localhost:5001/story-upload.html
3. **PWA Installation**: Use browser's "Add to Home Screen" feature

### 3. Test PWA Features

1. **Offline Mode**: 
   - Load the app
   - Disconnect internet
   - Navigate between pages (should work offline)

2. **Service Worker**:
   - Open browser dev tools
   - Check Application > Service Workers
   - Verify service worker is registered

### 4. Test Error Handling

1. **API Errors**: Try submitting invalid story data
2. **Network Errors**: Disconnect internet and try API calls
3. **Validation Errors**: Submit incomplete story forms

## File Structure

```
backend/
├── src/
│   ├── main.py              # Main Flask application with API endpoints
│   ├── story.py             # Story model with enhanced validation
│   └── sample_story.py      # Sample data
├── static/
│   ├── index.html           # Main PWA page
│   ├── story-upload.html    # Story creation interface
│   ├── manifest.json        # Enhanced PWA manifest
│   └── sw.js               # Service worker
├── database/
│   └── app.db              # SQLite database
├── requirements.txt         # Python dependencies
├── .env.example            # Environment template
├── populate_db.py          # Database initialization
└── test_api.py             # API testing script
```

## Next Steps (Phase 2)

The following improvements are planned for Phase 2:
1. Authentication system
2. Performance optimization with caching
3. Comprehensive test suite
4. Database optimization with indexes

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
2. **Database Errors**: Run `populate_db.py` to initialize
3. **CORS Errors**: Check ALLOWED_ORIGINS in .env file
4. **Service Worker Issues**: Clear browser cache and reload

### Debug Mode

Set `DEBUG=true` in .env for detailed error messages during development.

### Logs

Check the console output for detailed error information and API request logs.
