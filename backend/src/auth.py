from functools import wraps
from flask import jsonify, request, current_app
from flask_jwt_extended import (
    J<PERSON><PERSON>anager, jwt_required, create_access_token, create_refresh_token,
    get_jwt_identity, get_jwt, verify_jwt_in_request
)
from datetime import datetime, timedelta
import uuid

from user import User, UserSession, UserRole, db

# JWT Configuration
jwt = JWTManager()

def init_jwt(app):
    """Initialize JWT with the Flask app"""
    jwt.init_app(app)
    
    # Configure JWT settings
    app.config['JWT_SECRET_KEY'] = app.config['SECRET_KEY']
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    app.config['JWT_BLACKLIST_ENABLED'] = True
    app.config['JWT_BLACKLIST_TOKEN_CHECKS'] = ['access', 'refresh']

@jwt.token_in_blocklist_loader
def check_if_token_revoked(jwt_header, jwt_payload):
    """Check if a JWT token has been revoked"""
    jti = jwt_payload['jti']
    session = UserSession.query.filter_by(token_jti=jti).first()
    return session is None or session.is_revoked or session.is_expired()

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    """Handle expired tokens"""
    return jsonify({
        'success': False,
        'error': 'Token has expired',
        'message': 'Please log in again'
    }), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    """Handle invalid tokens"""
    return jsonify({
        'success': False,
        'error': 'Invalid token',
        'message': 'Please provide a valid token'
    }), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    """Handle missing tokens"""
    return jsonify({
        'success': False,
        'error': 'Authorization required',
        'message': 'Please log in to access this resource'
    }), 401

@jwt.revoked_token_loader
def revoked_token_callback(jwt_header, jwt_payload):
    """Handle revoked tokens"""
    return jsonify({
        'success': False,
        'error': 'Token has been revoked',
        'message': 'Please log in again'
    }), 401

def create_tokens(user):
    """Create access and refresh tokens for a user"""
    # Create JTI (JWT ID) for session tracking
    access_jti = str(uuid.uuid4())
    refresh_jti = str(uuid.uuid4())
    
    # Create tokens
    access_token = create_access_token(
        identity=user.id,
        additional_claims={'role': user.role.value},
        additional_headers={'jti': access_jti}
    )
    refresh_token = create_refresh_token(
        identity=user.id,
        additional_headers={'jti': refresh_jti}
    )
    
    # Store session information
    access_expires = datetime.utcnow() + current_app.config['JWT_ACCESS_TOKEN_EXPIRES']
    refresh_expires = datetime.utcnow() + current_app.config['JWT_REFRESH_TOKEN_EXPIRES']
    
    # Create session records
    access_session = UserSession(user.id, access_jti, access_expires)
    refresh_session = UserSession(user.id, refresh_jti, refresh_expires)
    
    db.session.add(access_session)
    db.session.add(refresh_session)
    db.session.commit()
    
    return {
        'access_token': access_token,
        'refresh_token': refresh_token,
        'expires_in': int(current_app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds())
    }

def revoke_token(jti):
    """Revoke a token by its JTI"""
    session = UserSession.query.filter_by(token_jti=jti).first()
    if session:
        session.revoke()
        return True
    return False

def revoke_all_user_tokens(user_id):
    """Revoke all tokens for a specific user"""
    sessions = UserSession.query.filter_by(user_id=user_id).all()
    for session in sessions:
        session.revoke()
    db.session.commit()
    return len(sessions)

def get_current_user():
    """Get the current authenticated user"""
    try:
        verify_jwt_in_request()
        user_id = get_jwt_identity()
        return User.query.get(user_id)
    except:
        return None

def require_role(required_role):
    """Decorator to require a specific role"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user = get_current_user()
            if not current_user:
                return jsonify({
                    'success': False,
                    'error': 'User not found'
                }), 401
            
            if isinstance(required_role, str):
                required_role_enum = UserRole(required_role)
            else:
                required_role_enum = required_role
            
            if not current_user.has_role(required_role_enum):
                return jsonify({
                    'success': False,
                    'error': 'Insufficient permissions',
                    'message': f'This action requires {required_role_enum.value} role'
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_author():
    """Decorator to require author role or higher"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user = get_current_user()
            if not current_user:
                return jsonify({
                    'success': False,
                    'error': 'User not found'
                }), 401
            
            if not current_user.can_create_stories():
                return jsonify({
                    'success': False,
                    'error': 'Insufficient permissions',
                    'message': 'This action requires author privileges'
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_story_ownership():
    """Decorator to require ownership of a story or admin role"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user = get_current_user()
            if not current_user:
                return jsonify({
                    'success': False,
                    'error': 'User not found'
                }), 401
            
            # Get story_id from URL parameters
            story_id = kwargs.get('story_id') or request.view_args.get('story_id')
            if not story_id:
                return jsonify({
                    'success': False,
                    'error': 'Story ID required'
                }), 400
            
            from story import Story
            story = Story.query.get(story_id)
            if not story:
                return jsonify({
                    'success': False,
                    'error': 'Story not found'
                }), 404
            
            if not current_user.can_edit_story(story):
                return jsonify({
                    'success': False,
                    'error': 'Insufficient permissions',
                    'message': 'You can only edit your own stories'
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def optional_auth():
    """Decorator for optional authentication - provides user if authenticated"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            current_user = get_current_user()
            kwargs['current_user'] = current_user
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def validate_registration_data(data):
    """Validate user registration data"""
    errors = []
    
    # Required fields
    required_fields = ['username', 'email', 'password']
    for field in required_fields:
        if field not in data or not data[field]:
            errors.append(f"Missing required field: {field}")
    
    if errors:
        return errors
    
    # Validate username
    valid, error = User.validate_username(data['username'])
    if not valid:
        errors.append(error)
    
    # Validate email
    valid, error = User.validate_email(data['email'])
    if not valid:
        errors.append(error)
    
    # Validate password
    valid, error = User.validate_password(data['password'])
    if not valid:
        errors.append(error)
    
    # Validate role if provided
    if 'role' in data:
        try:
            UserRole(data['role'])
        except ValueError:
            errors.append(f"Invalid role. Must be one of: {', '.join([r.value for r in UserRole])}")
    
    return errors

def validate_login_data(data):
    """Validate user login data"""
    errors = []
    
    # Required fields
    required_fields = ['username', 'password']
    for field in required_fields:
        if field not in data or not data[field]:
            errors.append(f"Missing required field: {field}")
    
    return errors
