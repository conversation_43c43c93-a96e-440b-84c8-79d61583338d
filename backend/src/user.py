from flask_sqlalchemy import SQLAlchemy
from flask_bcrypt import Bcrypt
from datetime import datetime, timedelta
import re
from enum import Enum

# Import db from story.py to maintain consistency
from story import db

bcrypt = Bcrypt()

class UserRole(Enum):
    """User roles for role-based access control"""
    READER = "reader"
    AUTHOR = "author"
    ADMIN = "admin"

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.READER)
    
    # Profile information
    display_name = db.Column(db.String(100), nullable=True)
    bio = db.Column(db.Text, nullable=True)
    avatar_url = db.Column(db.String(255), nullable=True)
    
    # Account status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_verified = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    stories = db.relationship('Story', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, username, email, password, role=UserRole.READER, display_name=None):
        self.username = username
        self.email = email
        self.set_password(password)
        self.role = role
        self.display_name = display_name or username
    
    def set_password(self, password):
        """Hash and set the user's password"""
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
    
    def check_password(self, password):
        """Check if the provided password matches the user's password"""
        return bcrypt.check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """Update the last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def has_role(self, role):
        """Check if user has a specific role"""
        if isinstance(role, str):
            role = UserRole(role)
        return self.role == role
    
    def can_create_stories(self):
        """Check if user can create stories"""
        return self.role in [UserRole.AUTHOR, UserRole.ADMIN]
    
    def can_edit_story(self, story):
        """Check if user can edit a specific story"""
        if self.role == UserRole.ADMIN:
            return True
        return self.role == UserRole.AUTHOR and story.author_id == self.id
    
    def can_delete_story(self, story):
        """Check if user can delete a specific story"""
        if self.role == UserRole.ADMIN:
            return True
        return self.role == UserRole.AUTHOR and story.author_id == self.id
    
    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary for JSON serialization"""
        user_dict = {
            'id': self.id,
            'username': self.username,
            'display_name': self.display_name,
            'role': self.role.value,
            'bio': self.bio,
            'avatar_url': self.avatar_url,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
        
        if include_sensitive:
            user_dict.update({
                'email': self.email,
                'is_active': self.is_active,
                'updated_at': self.updated_at.isoformat() if self.updated_at else None
            })
        
        return user_dict
    
    @staticmethod
    def validate_username(username):
        """Validate username format"""
        if not username or len(username) < 3 or len(username) > 80:
            return False, "Username must be between 3 and 80 characters"
        
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            return False, "Username can only contain letters, numbers, underscores, and hyphens"
        
        return True, None
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        if not email or len(email) > 120:
            return False, "Email must be provided and less than 120 characters"
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return False, "Invalid email format"
        
        return True, None
    
    @staticmethod
    def validate_password(password):
        """Validate password strength"""
        if not password or len(password) < 8:
            return False, "Password must be at least 8 characters long"
        
        if len(password) > 128:
            return False, "Password must be less than 128 characters"
        
        # Check for at least one letter and one number
        if not re.search(r'[a-zA-Z]', password) or not re.search(r'\d', password):
            return False, "Password must contain at least one letter and one number"
        
        return True, None
    
    @classmethod
    def create_user(cls, username, email, password, role=UserRole.READER, display_name=None):
        """Create a new user with validation"""
        errors = []
        
        # Validate username
        valid, error = cls.validate_username(username)
        if not valid:
            errors.append(error)
        elif cls.query.filter_by(username=username).first():
            errors.append("Username already exists")
        
        # Validate email
        valid, error = cls.validate_email(email)
        if not valid:
            errors.append(error)
        elif cls.query.filter_by(email=email).first():
            errors.append("Email already exists")
        
        # Validate password
        valid, error = cls.validate_password(password)
        if not valid:
            errors.append(error)
        
        if errors:
            return None, errors
        
        # Create user
        user = cls(username=username, email=email, password=password, role=role, display_name=display_name)
        return user, None

class UserSession(db.Model):
    __tablename__ = 'user_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    token_jti = db.Column(db.String(36), nullable=False, unique=True, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    is_revoked = db.Column(db.Boolean, default=False, nullable=False)
    
    user = db.relationship('User', backref='sessions')
    
    def __init__(self, user_id, token_jti, expires_at):
        self.user_id = user_id
        self.token_jti = token_jti
        self.expires_at = expires_at
    
    def is_expired(self):
        """Check if the session is expired"""
        return datetime.utcnow() > self.expires_at
    
    def revoke(self):
        """Revoke the session"""
        self.is_revoked = True
        db.session.commit()
    
    @classmethod
    def cleanup_expired(cls):
        """Remove expired sessions"""
        expired_sessions = cls.query.filter(cls.expires_at < datetime.utcnow()).all()
        for session in expired_sessions:
            db.session.delete(session)
        db.session.commit()
        return len(expired_sessions)
