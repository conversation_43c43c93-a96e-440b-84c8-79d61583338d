import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory, request, jsonify
from flask_cors import CORS
from flask_bcrypt import Bcrypt
from story import db, Story
from user import User, UserSession, UserRole
from auth import (
    init_jwt, create_tokens, revoke_token, revoke_all_user_tokens,
    get_current_user, require_role, require_author, require_story_ownership,
    optional_auth, validate_registration_data, validate_login_data
)
import uuid
import json
from dotenv import load_dotenv
import secrets

# Load environment variables
load_dotenv()

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static'))

# Security Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', secrets.token_urlsafe(32))

# Enable CORS with specific origins for security
allowed_origins = os.environ.get('ALLOWED_ORIGINS', 'http://localhost:5001,http://127.0.0.1:5001').split(',')
CORS(app, origins=allowed_origins)

# Initialize authentication
init_jwt(app)

# Input validation and sanitization
import re
import html

def sanitize_string(text, max_length=None):
    """Sanitize string input to prevent XSS and other attacks"""
    if not isinstance(text, str):
        return ""

    # Remove HTML tags and escape special characters
    text = html.escape(text.strip())

    # Remove potentially dangerous characters
    text = re.sub(r'[<>"\']', '', text)

    # Limit length if specified
    if max_length and len(text) > max_length:
        text = text[:max_length]

    return text

def validate_story_data(data):
    """Validate and sanitize story data"""
    errors = []

    # Required fields validation
    required_fields = ['title', 'description', 'genre', 'reading_time', 'theme', 'style', 'start_page_id', 'pages']
    for field in required_fields:
        if field not in data or not data[field]:
            errors.append(f"Missing required field: {field}")

    if errors:
        return errors, None

    # Sanitize and validate individual fields
    sanitized_data = {}

    # Title validation
    sanitized_data['title'] = sanitize_string(data['title'], 200)
    if len(sanitized_data['title']) < 3:
        errors.append("Title must be at least 3 characters long")

    # Description validation
    sanitized_data['description'] = sanitize_string(data['description'], 1000)
    if len(sanitized_data['description']) < 10:
        errors.append("Description must be at least 10 characters long")

    # Genre validation
    if data['genre'] not in AVAILABLE_GENRES:
        errors.append(f"Invalid genre. Must be one of: {', '.join(AVAILABLE_GENRES)}")
    sanitized_data['genre'] = data['genre']

    # Reading time validation
    try:
        reading_time = int(data['reading_time'])
        if reading_time < 1 or reading_time > 300:
            errors.append("Reading time must be between 1 and 300 minutes")
        sanitized_data['reading_time'] = reading_time
    except (ValueError, TypeError):
        errors.append("Reading time must be a valid number")

    # Theme and style validation
    sanitized_data['theme'] = sanitize_string(data['theme'], 200)
    sanitized_data['style'] = sanitize_string(data['style'], 200)
    sanitized_data['start_page_id'] = sanitize_string(data['start_page_id'], 50)

    # Author validation (optional)
    if 'author' in data:
        sanitized_data['author'] = sanitize_string(data['author'], 100)

    # Pages validation
    if not isinstance(data['pages'], dict):
        errors.append("Pages must be a valid object")
    else:
        sanitized_data['pages'] = data['pages']  # Pages will be validated by Story model

    # ID validation (optional)
    if 'id' in data:
        sanitized_data['id'] = sanitize_string(data['id'], 100)
        if not re.match(r'^[a-zA-Z0-9_-]+$', sanitized_data['id']):
            errors.append("ID can only contain letters, numbers, underscores, and hyphens")

    return errors, sanitized_data

# API Routes
# Available genres for stories
AVAILABLE_GENRES = ["Romance", "Fantasy", "Mystery", "Sci-Fi", "Thriller", "Abenteuer", "Drama", "Horror"]
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(os.path.dirname(__file__)), 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)
with app.app_context():
    db.create_all()

# Error Handlers

@app.errorhandler(400)
def bad_request(error):
    return jsonify({
        'success': False,
        'error': 'Bad Request',
        'message': 'The request could not be understood by the server'
    }), 400

@app.errorhandler(401)
def unauthorized(error):
    return jsonify({
        'success': False,
        'error': 'Unauthorized',
        'message': 'Authentication required'
    }), 401

@app.errorhandler(403)
def forbidden(error):
    return jsonify({
        'success': False,
        'error': 'Forbidden',
        'message': 'Access denied'
    }), 403

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Not Found',
        'message': 'The requested resource was not found'
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'error': 'Method Not Allowed',
        'message': 'The method is not allowed for this endpoint'
    }), 405

@app.errorhandler(500)
def internal_server_error(error):
    db.session.rollback()
    return jsonify({
        'success': False,
        'error': 'Internal Server Error',
        'message': 'An unexpected error occurred'
    }), 500

@app.errorhandler(Exception)
def handle_exception(error):
    """Handle unexpected exceptions"""
    db.session.rollback()

    # Log the error (in production, use proper logging)
    print(f"Unexpected error: {str(error)}")

    return jsonify({
        'success': False,
        'error': 'Unexpected Error',
        'message': 'An unexpected error occurred. Please try again later.'
    }), 500

# API Endpoints

@app.route('/api/stories', methods=['GET'])
def get_stories():
    """Get all published stories with optional filtering"""
    try:
        # Get query parameters for filtering
        genre = request.args.get('genre')
        theme = request.args.get('theme')
        style = request.args.get('style')

        # Build query
        query = Story.query.filter_by(is_published=True)

        if genre and genre != 'alle':
            query = query.filter_by(genre=genre)
        if theme:
            query = query.filter(Story.theme.contains(theme))
        if style:
            query = query.filter(Story.style.contains(style))

        stories = query.order_by(Story.created_at.desc()).all()

        # Convert to dict format
        stories_data = []
        for story in stories:
            story_dict = story.to_dict()
            # Remove pages from list view for performance
            story_dict.pop('pages', None)
            stories_data.append(story_dict)

        return jsonify({
            'success': True,
            'stories': stories_data,
            'count': len(stories_data)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error fetching stories: {str(e)}'
        }), 500

@app.route('/api/stories/<story_id>', methods=['GET'])
def get_story(story_id):
    """Get a specific story by ID"""
    try:
        story = Story.query.get(story_id)
        if not story:
            return jsonify({
                'success': False,
                'error': 'Story not found'
            }), 404

        if not story.is_published:
            return jsonify({
                'success': False,
                'error': 'Story not published'
            }), 403

        return jsonify({
            'success': True,
            'story': story.to_dict()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error fetching story: {str(e)}'
        }), 500

@app.route('/api/stories', methods=['POST'])
@optional_auth()
def create_story(current_user=None):
    """Create a new story"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        # Check if user is authenticated and has author privileges
        if current_user and not current_user.can_create_stories():
            return jsonify({
                'success': False,
                'error': 'Insufficient permissions',
                'message': 'Only authors can create stories'
            }), 403

        # Validate and sanitize input data
        validation_errors, sanitized_data = validate_story_data(data)
        if validation_errors or sanitized_data is None:
            return jsonify({
                'success': False,
                'error': 'Input validation failed',
                'validation_errors': validation_errors or ['Unknown validation error']
            }), 400

        # Generate unique ID if not provided
        story_id = sanitized_data.get('id', str(uuid.uuid4()))

        # Check if story with this ID already exists
        existing_story = Story.query.get(story_id)
        if existing_story:
            return jsonify({
                'success': False,
                'error': 'Story with this ID already exists'
            }), 409

        # Determine author information
        author_id = None
        author_name = 'Anonymous'

        if current_user:
            author_id = current_user.id
            author_name = current_user.display_name
        elif 'author' in sanitized_data:
            author_name = sanitized_data['author']

        # Create new story with sanitized data
        story = Story(
            id=story_id,
            title=sanitized_data['title'],
            description=sanitized_data['description'],
            genre=sanitized_data['genre'],
            reading_time=sanitized_data['reading_time'],
            theme=sanitized_data['theme'],
            style=sanitized_data['style'],
            start_page_id=sanitized_data['start_page_id'],
            pages=sanitized_data['pages'],
            author=author_name,
            author_id=author_id
        )

        # Set published status (only authenticated authors can publish immediately)
        if current_user and current_user.can_create_stories():
            story.is_published = data.get('is_published', False)
        else:
            story.is_published = False  # Anonymous stories are not published by default

        # Validate story structure
        structure_errors = story.validate_story_structure()
        if structure_errors:
            return jsonify({
                'success': False,
                'error': 'Story structure validation failed',
                'validation_errors': structure_errors
            }), 400

        # Save to database
        db.session.add(story)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Story created successfully',
            'story_id': story_id,
            'is_published': story.is_published,
            'author': {
                'id': author_id,
                'name': author_name
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'Error creating story: {str(e)}'
        }), 500

@app.route('/api/stories/<story_id>/validate', methods=['POST'])
def validate_story(story_id):
    """Validate a story structure"""
    try:
        story = Story.query.get(story_id)
        if not story:
            return jsonify({
                'success': False,
                'error': 'Story not found'
            }), 404

        validation_errors = story.validate_story_structure()

        return jsonify({
            'success': True,
            'is_valid': len(validation_errors) == 0,
            'validation_errors': validation_errors,
            'statistics': {
                'page_count': story.get_page_count(),
                'ending_count': story.get_ending_count(),
                'choice_count': story.get_choice_count()
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error validating story: {str(e)}'
        }), 500

@app.route('/api/stories/<story_id>/publish', methods=['POST'])
@require_story_ownership()
def publish_story(story_id):
    """Publish a story (requires ownership)"""
    try:
        story = Story.query.get(story_id)
        if not story:
            return jsonify({
                'success': False,
                'error': 'Story not found'
            }), 404

        # Validate before publishing
        validation_errors = story.validate_story_structure()
        if validation_errors:
            return jsonify({
                'success': False,
                'error': 'Cannot publish story with validation errors',
                'validation_errors': validation_errors
            }), 400

        story.is_published = True
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Story published successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'Error publishing story: {str(e)}'
        }), 500

@app.route('/api/stories/<story_id>', methods=['PUT'])
@require_story_ownership()
def update_story(story_id):
    """Update a story (requires ownership)"""
    try:
        story = Story.query.get(story_id)
        if not story:
            return jsonify({
                'success': False,
                'error': 'Story not found'
            }), 404

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        # Validate and sanitize input data
        validation_errors, sanitized_data = validate_story_data(data)
        if validation_errors or sanitized_data is None:
            return jsonify({
                'success': False,
                'error': 'Input validation failed',
                'validation_errors': validation_errors or ['Unknown validation error']
            }), 400

        # Update story fields
        story.title = sanitized_data['title']
        story.description = sanitized_data['description']
        story.genre = sanitized_data['genre']
        story.reading_time = sanitized_data['reading_time']
        story.theme = sanitized_data['theme']
        story.style = sanitized_data['style']
        story.start_page_id = sanitized_data['start_page_id']
        story.pages = sanitized_data['pages']

        # Validate story structure
        structure_errors = story.validate_story_structure()
        if structure_errors:
            return jsonify({
                'success': False,
                'error': 'Story structure validation failed',
                'validation_errors': structure_errors
            }), 400

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Story updated successfully',
            'story': story.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'Error updating story: {str(e)}'
        }), 500

@app.route('/api/stories/<story_id>', methods=['DELETE'])
@require_story_ownership()
def delete_story(story_id):
    """Delete a story (requires ownership)"""
    try:
        story = Story.query.get(story_id)
        if not story:
            return jsonify({
                'success': False,
                'error': 'Story not found'
            }), 404

        db.session.delete(story)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Story deleted successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'Error deleting story: {str(e)}'
        }), 500

@app.route('/api/users/<int:user_id>/stories', methods=['GET'])
@optional_auth()
def get_user_stories(user_id, current_user=None):
    """Get stories by a specific user"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'success': False,
                'error': 'User not found'
            }), 404

        # Build query - show only published stories unless it's the user's own stories
        query = Story.query.filter_by(author_id=user_id)

        if not current_user or current_user.id != user_id:
            # Show only published stories for other users
            query = query.filter_by(is_published=True)

        stories = query.order_by(Story.created_at.desc()).all()

        # Convert to dict format
        stories_data = []
        for story in stories:
            story_dict = story.to_dict()
            # Remove pages from list view for performance
            story_dict.pop('pages', None)
            stories_data.append(story_dict)

        return jsonify({
            'success': True,
            'stories': stories_data,
            'user': user.to_dict(),
            'count': len(stories_data)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error fetching user stories: {str(e)}'
        }), 500

@app.route('/api/genres', methods=['GET'])
def get_genres():
    """Get all available genres"""
    return jsonify({
        'success': True,
        'genres': AVAILABLE_GENRES
    })

# Authentication Endpoints

@app.route('/api/auth/register', methods=['POST'])
def register():
    """Register a new user"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        # Validate input data
        validation_errors = validate_registration_data(data)
        if validation_errors:
            return jsonify({
                'success': False,
                'error': 'Validation failed',
                'validation_errors': validation_errors
            }), 400

        # Create user
        role = UserRole.READER  # Default role
        if 'role' in data and data['role'] in ['author', 'reader']:
            role = UserRole(data['role'])

        user, errors = User.create_user(
            username=data['username'],
            email=data['email'],
            password=data['password'],
            role=role,
            display_name=data.get('display_name')
        )

        if errors:
            return jsonify({
                'success': False,
                'error': 'User creation failed',
                'validation_errors': errors
            }), 400

        # Save user to database
        db.session.add(user)
        db.session.commit()

        # Create tokens
        tokens = create_tokens(user)

        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'user': user.to_dict(),
            'tokens': tokens
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'Registration failed: {str(e)}'
        }), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """Authenticate user and return tokens"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        # Validate input data
        validation_errors = validate_login_data(data)
        if validation_errors:
            return jsonify({
                'success': False,
                'error': 'Validation failed',
                'validation_errors': validation_errors
            }), 400

        # Find user by username or email
        username_or_email = data['username']
        user = User.query.filter(
            (User.username == username_or_email) |
            (User.email == username_or_email)
        ).first()

        if not user or not user.check_password(data['password']):
            return jsonify({
                'success': False,
                'error': 'Invalid credentials'
            }), 401

        if not user.is_active:
            return jsonify({
                'success': False,
                'error': 'Account is deactivated'
            }), 401

        # Update last login
        user.update_last_login()

        # Create tokens
        tokens = create_tokens(user)

        return jsonify({
            'success': True,
            'message': 'Login successful',
            'user': user.to_dict(),
            'tokens': tokens
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Login failed: {str(e)}'
        }), 500

@app.route('/api/auth/logout', methods=['POST'])
@require_role('reader')  # Any authenticated user can logout
def logout():
    """Logout user and revoke current token"""
    try:
        from flask_jwt_extended import get_jwt

        jti = get_jwt()['jti']
        revoke_token(jti)

        return jsonify({
            'success': True,
            'message': 'Logged out successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Logout failed: {str(e)}'
        }), 500

@app.route('/api/auth/me', methods=['GET'])
@require_role('reader')  # Any authenticated user can access
def get_current_user_info():
    """Get current user information"""
    try:
        current_user = get_current_user()

        return jsonify({
            'success': True,
            'user': current_user.to_dict(include_sensitive=True)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to get user info: {str(e)}'
        }), 500

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


if __name__ == '__main__':
    # Get debug mode from environment variable
    debug_mode = os.environ.get('DEBUG', 'true').lower() == 'true'
    app.run(host='0.0.0.0', port=5001, debug=debug_mode)
