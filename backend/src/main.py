import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory, request, jsonify
from flask_cors import CORS
from story import db, Story
import uuid
import json
from dotenv import load_dotenv
import secrets

# Load environment variables
load_dotenv()

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static'))

# Security Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', secrets.token_urlsafe(32))

# Enable CORS with specific origins for security
allowed_origins = os.environ.get('ALLOWED_ORIGINS', 'http://localhost:5001,http://127.0.0.1:5001').split(',')
CORS(app, origins=allowed_origins)

# Input validation and sanitization
import re
import html

def sanitize_string(text, max_length=None):
    """Sanitize string input to prevent XSS and other attacks"""
    if not isinstance(text, str):
        return ""

    # Remove HTML tags and escape special characters
    text = html.escape(text.strip())

    # Remove potentially dangerous characters
    text = re.sub(r'[<>"\']', '', text)

    # Limit length if specified
    if max_length and len(text) > max_length:
        text = text[:max_length]

    return text

def validate_story_data(data):
    """Validate and sanitize story data"""
    errors = []

    # Required fields validation
    required_fields = ['title', 'description', 'genre', 'reading_time', 'theme', 'style', 'start_page_id', 'pages']
    for field in required_fields:
        if field not in data or not data[field]:
            errors.append(f"Missing required field: {field}")

    if errors:
        return errors, None

    # Sanitize and validate individual fields
    sanitized_data = {}

    # Title validation
    sanitized_data['title'] = sanitize_string(data['title'], 200)
    if len(sanitized_data['title']) < 3:
        errors.append("Title must be at least 3 characters long")

    # Description validation
    sanitized_data['description'] = sanitize_string(data['description'], 1000)
    if len(sanitized_data['description']) < 10:
        errors.append("Description must be at least 10 characters long")

    # Genre validation
    if data['genre'] not in AVAILABLE_GENRES:
        errors.append(f"Invalid genre. Must be one of: {', '.join(AVAILABLE_GENRES)}")
    sanitized_data['genre'] = data['genre']

    # Reading time validation
    try:
        reading_time = int(data['reading_time'])
        if reading_time < 1 or reading_time > 300:
            errors.append("Reading time must be between 1 and 300 minutes")
        sanitized_data['reading_time'] = reading_time
    except (ValueError, TypeError):
        errors.append("Reading time must be a valid number")

    # Theme and style validation
    sanitized_data['theme'] = sanitize_string(data['theme'], 200)
    sanitized_data['style'] = sanitize_string(data['style'], 200)
    sanitized_data['start_page_id'] = sanitize_string(data['start_page_id'], 50)

    # Author validation (optional)
    if 'author' in data:
        sanitized_data['author'] = sanitize_string(data['author'], 100)

    # Pages validation
    if not isinstance(data['pages'], dict):
        errors.append("Pages must be a valid object")
    else:
        sanitized_data['pages'] = data['pages']  # Pages will be validated by Story model

    # ID validation (optional)
    if 'id' in data:
        sanitized_data['id'] = sanitize_string(data['id'], 100)
        if not re.match(r'^[a-zA-Z0-9_-]+$', sanitized_data['id']):
            errors.append("ID can only contain letters, numbers, underscores, and hyphens")

    return errors, sanitized_data

# API Routes
# Available genres for stories
AVAILABLE_GENRES = ["Romance", "Fantasy", "Mystery", "Sci-Fi", "Thriller", "Abenteuer", "Drama", "Horror"]
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(os.path.dirname(__file__)), 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)
with app.app_context():
    db.create_all()

# Error Handlers

@app.errorhandler(400)
def bad_request(error):
    return jsonify({
        'success': False,
        'error': 'Bad Request',
        'message': 'The request could not be understood by the server'
    }), 400

@app.errorhandler(401)
def unauthorized(error):
    return jsonify({
        'success': False,
        'error': 'Unauthorized',
        'message': 'Authentication required'
    }), 401

@app.errorhandler(403)
def forbidden(error):
    return jsonify({
        'success': False,
        'error': 'Forbidden',
        'message': 'Access denied'
    }), 403

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Not Found',
        'message': 'The requested resource was not found'
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'error': 'Method Not Allowed',
        'message': 'The method is not allowed for this endpoint'
    }), 405

@app.errorhandler(500)
def internal_server_error(error):
    db.session.rollback()
    return jsonify({
        'success': False,
        'error': 'Internal Server Error',
        'message': 'An unexpected error occurred'
    }), 500

@app.errorhandler(Exception)
def handle_exception(error):
    """Handle unexpected exceptions"""
    db.session.rollback()

    # Log the error (in production, use proper logging)
    print(f"Unexpected error: {str(error)}")

    return jsonify({
        'success': False,
        'error': 'Unexpected Error',
        'message': 'An unexpected error occurred. Please try again later.'
    }), 500

# API Endpoints

@app.route('/api/stories', methods=['GET'])
def get_stories():
    """Get all published stories with optional filtering"""
    try:
        # Get query parameters for filtering
        genre = request.args.get('genre')
        theme = request.args.get('theme')
        style = request.args.get('style')

        # Build query
        query = Story.query.filter_by(is_published=True)

        if genre and genre != 'alle':
            query = query.filter_by(genre=genre)
        if theme:
            query = query.filter(Story.theme.contains(theme))
        if style:
            query = query.filter(Story.style.contains(style))

        stories = query.order_by(Story.created_at.desc()).all()

        # Convert to dict format
        stories_data = []
        for story in stories:
            story_dict = story.to_dict()
            # Remove pages from list view for performance
            story_dict.pop('pages', None)
            stories_data.append(story_dict)

        return jsonify({
            'success': True,
            'stories': stories_data,
            'count': len(stories_data)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error fetching stories: {str(e)}'
        }), 500

@app.route('/api/stories/<story_id>', methods=['GET'])
def get_story(story_id):
    """Get a specific story by ID"""
    try:
        story = Story.query.get(story_id)
        if not story:
            return jsonify({
                'success': False,
                'error': 'Story not found'
            }), 404

        if not story.is_published:
            return jsonify({
                'success': False,
                'error': 'Story not published'
            }), 403

        return jsonify({
            'success': True,
            'story': story.to_dict()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error fetching story: {str(e)}'
        }), 500

@app.route('/api/stories', methods=['POST'])
def create_story():
    """Create a new story"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        # Validate and sanitize input data
        validation_errors, sanitized_data = validate_story_data(data)
        if validation_errors or sanitized_data is None:
            return jsonify({
                'success': False,
                'error': 'Input validation failed',
                'validation_errors': validation_errors or ['Unknown validation error']
            }), 400

        # Generate unique ID if not provided
        story_id = sanitized_data.get('id', str(uuid.uuid4()))

        # Check if story with this ID already exists
        existing_story = Story.query.get(story_id)
        if existing_story:
            return jsonify({
                'success': False,
                'error': 'Story with this ID already exists'
            }), 409

        # Create new story with sanitized data
        story = Story(
            id=story_id,
            title=sanitized_data['title'],
            description=sanitized_data['description'],
            genre=sanitized_data['genre'],
            reading_time=sanitized_data['reading_time'],
            theme=sanitized_data['theme'],
            style=sanitized_data['style'],
            start_page_id=sanitized_data['start_page_id'],
            pages=sanitized_data['pages'],
            author=sanitized_data.get('author', 'Anonymous')
        )

        # Set published status
        story.is_published = data.get('is_published', False)

        # Validate story structure
        structure_errors = story.validate_story_structure()
        if structure_errors:
            return jsonify({
                'success': False,
                'error': 'Story structure validation failed',
                'validation_errors': structure_errors
            }), 400

        # Save to database
        db.session.add(story)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Story created successfully',
            'story_id': story_id,
            'is_published': story.is_published
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'Error creating story: {str(e)}'
        }), 500

@app.route('/api/stories/<story_id>/validate', methods=['POST'])
def validate_story(story_id):
    """Validate a story structure"""
    try:
        story = Story.query.get(story_id)
        if not story:
            return jsonify({
                'success': False,
                'error': 'Story not found'
            }), 404

        validation_errors = story.validate_story_structure()

        return jsonify({
            'success': True,
            'is_valid': len(validation_errors) == 0,
            'validation_errors': validation_errors,
            'statistics': {
                'page_count': story.get_page_count(),
                'ending_count': story.get_ending_count(),
                'choice_count': story.get_choice_count()
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error validating story: {str(e)}'
        }), 500

@app.route('/api/stories/<story_id>/publish', methods=['POST'])
def publish_story(story_id):
    """Publish a story"""
    try:
        story = Story.query.get(story_id)
        if not story:
            return jsonify({
                'success': False,
                'error': 'Story not found'
            }), 404

        # Validate before publishing
        validation_errors = story.validate_story_structure()
        if validation_errors:
            return jsonify({
                'success': False,
                'error': 'Cannot publish story with validation errors',
                'validation_errors': validation_errors
            }), 400

        story.is_published = True
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Story published successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'Error publishing story: {str(e)}'
        }), 500

@app.route('/api/genres', methods=['GET'])
def get_genres():
    """Get all available genres"""
    return jsonify({
        'success': True,
        'genres': AVAILABLE_GENRES
    })

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


if __name__ == '__main__':
    # Get debug mode from environment variable
    debug_mode = os.environ.get('DEBUG', 'true').lower() == 'true'
    app.run(host='0.0.0.0', port=5001, debug=debug_mode)
