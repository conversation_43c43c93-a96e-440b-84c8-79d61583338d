# StoryChoice – Interaktive Geschichten als Progressive Web App

## Projektüberblick
StoryChoice ist eine Progressive Web App (PWA) für interaktive Geschichten, bei der Nutzer durch Entscheidungen den Verlauf der Story aktiv beeinflussen. Das System unterstützt komplexe Entscheidungsbäume, individuelle Pfade und eine finale Konvergenz mit mehreren Enden. Autoren können eigene Geschichten über ein intuitives Upload-System erstellen und veröffentlichen.

---

## Hauptfeatures
- **Interaktive Geschichten mit Entscheidungsbäumen**
- **Story-Upload-System** für Autoren (Web-Editor)
- **Filterbare Story-Übersicht** (Genre, Lesedauer)
- **Responsives, modernes Design** (Mobile-First, Dark Theme)
- **PWA-Funktionalität** (Installierbar, Offline-Modus vorbereitet)
- **Barrierefreiheit** (hohe Kontraste, Keyboard-Navigation)

---

## Technische Architektur
- **Frontend:**
  - `backend/static/index.html` – Hauptwebseite mit Story-Übersicht und Story-Player
  - `backend/static/index_mit_filtern.html` – Erweiterte Version mit Filterfunktionen
  - `backend/static/story-upload.html` – Story-Upload- und Editor-Interface für Autoren
  - **Design:** Moderne Typografie, Gradient-Akzente, Animationen
- **Backend:**
  - Python (Flask), REST-API (`backend/src/main.py`, `backend/src/story.py`)
  - Story-Datenmodell: Stories mit Metadaten und verschachtelten Seiten (siehe `docs/Analyse des Story-Schemas und Datenmodellierung.md`)
  - Speicherung als SQLAlchemy-Modelle, Seitenstruktur als JSON
  - API-Endpunkte für Story-Listing, Detail, Upload, Validierung, Filter
- **PWA:**
  - `backend/static/manifest.json` – App-Manifest für Installierbarkeit
  - Service Worker (in Vorbereitung)

---

## Datenmodell (Kurzüberblick)
Jede Story besteht aus:
- Metadaten: Titel, Beschreibung, Genre, Lesedauer, Thema, Stil, Autor
- Seiten: Jede Seite hat Text (2–4 Sätze), Entscheidungen (`choices`), optionale Medien, End-Tags
- Entscheidungsstruktur: Baum mit eindeutigen Seiten-IDs, finale Konvergenz zu 3 Enden

Beispiel (vereinfacht):
```json
{
  "id": "story_id",
  "title": "Titel der Geschichte",
  "pages": {
    "1": { "id": "1", "text": "...", "choices": [{"text": "A", "next_page_id": "2A"}] },
    "2A": { "id": "2A", "text": "...", "choices": [...] },
    "final_convergence": { "id": "final_convergence", "choices": [...] },
    "happy_ending_1": { "id": "happy_ending_1", "is_ending": true, "ending_type": "happy_triumph" }
  }
}
```

---

## Setup & Entwicklung
### Voraussetzungen
- Python 3.11+
- Flask, Flask-CORS, Flask-SQLAlchemy
- (Optional) Virtuelle Umgebung empfohlen

### Lokaler Start (Backend & Frontend)
1. **Backend installieren & starten:**
   ```bash
   cd backend
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   python src/main.py
   ```
2. **Frontend öffnen:**
   - `backend/static/index.html` oder `backend/static/index_mit_filtern.html` im Browser öffnen
   - Story-Upload: `backend/static/story-upload.html`
   - Standardmäßig erreichbar unter: [http://localhost:5001/](http://localhost:5001/)

### Deployment (Empfehlung)
- Für Produktion: Gunicorn oder ähnlichen WSGI-Server nutzen
- Statische Dateien werden vom Flask-Backend ausgeliefert

---

## Wichtige Dateien & Ordner
- `backend/static/index.html` – Hauptwebseite
- `backend/static/index_mit_filtern.html` – Mit Filterfunktion
- `backend/static/story-upload.html` – Story-Editor für Autoren
- `backend/src/main.py` – Flask-Backend (API, Routing, DB)
- `backend/src/story.py` – Datenmodell für Stories
- `backend/static/manifest.json` – PWA-Manifest
- `docs/Analyse des Story-Schemas und Datenmodellierung.md` – Datenmodell- und Schema-Analyse
- `docs/Story-Upload-System für Interaktive Geschichten.md` – Vollständige Systemdokumentation
- `todo.md` – Aufgabenliste

---

## Weiterführende Dokumentation
- **Story-Schema & Datenmodell:** Siehe `docs/Analyse des Story-Schemas und Datenmodellierung.md`
- **Systemarchitektur & API:** Siehe `docs/Story-Upload-System für Interaktive Geschichten.md`
- **Feature- und Marktanalyse:** Siehe `docs/comprehensive_analysis_report.md`
- **Design & UX:** Siehe `docs/Deutsche Webseitenbeschreibung für StoryChoice.md`

---

## Hinweise
- Das Projekt befindet sich in aktiver Entwicklung. Einige PWA-Features (z.B. Service Worker) sind vorbereitet, aber noch nicht final integriert.
- Für Fragen oder Beiträge bitte die Dokumentation lesen und Issues/PRs anlegen. 